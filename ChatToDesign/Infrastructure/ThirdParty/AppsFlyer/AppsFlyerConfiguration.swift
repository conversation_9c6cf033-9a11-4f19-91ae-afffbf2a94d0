//
//  AppsFlyerConfiguration.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/1/21.
//

import Foundation

/// AppsFlyer SDK configuration model
public struct AppsFlyerConfiguration {

  // MARK: - Core Configuration

  /// AppsFlyer developer key
  public let devKey: String

  /// Apple App ID (without 'id' prefix)
  public let appleAppID: String

  /// Enable debug mode
  public let isDebugEnabled: Bool

  /// Wait timeout for ATT user authorization (seconds)
  public let attWaitTimeout: TimeInterval

  /// Custom user ID for attribution
  public let customUserID: String?

  // MARK: - Feature Flags

  /// Enable deep linking
  public let deepLinkingEnabled: Bool

  /// Enable uninstall measurement
  public let uninstallMeasurementEnabled: Bool

  /// Enable conversion data collection
  public let conversionDataEnabled: Bool

  /// Enable ad revenue tracking
  public let adRevenueTrackingEnabled: Bool

  // MARK: - Privacy Settings

  /// Anonymous user mode
  public let anonymousUserMode: Bool

  // MARK: - Advanced Settings

  /// Minimum time between sessions (seconds)
  public let minTimeBetweenSessions: TimeInterval

  /// Additional custom properties
  public let customProperties: [String: Any]

  // MARK: - Initialization

  /// Initialize AppsFlyer configuration
  /// - Parameters:
  ///   - devKey: AppsFlyer developer key
  ///   - appleAppID: Apple App ID
  ///   - isDebugEnabled: Enable debug mode
  ///   - attWaitTimeout: ATT wait timeout
  ///   - customUserID: Custom user ID
  ///   - deepLinkingEnabled: Enable deep linking
  ///   - uninstallMeasurementEnabled: Enable uninstall measurement
  ///   - conversionDataEnabled: Enable conversion data
  ///   - adRevenueTrackingEnabled: Enable ad revenue tracking
  ///   - anonymousUserMode: Anonymous user mode
  ///   - minTimeBetweenSessions: Min time between sessions
  ///   - customProperties: Additional properties
  public init(
    devKey: String,
    appleAppID: String,
    isDebugEnabled: Bool = false,
    attWaitTimeout: TimeInterval = 60,
    customUserID: String? = nil,
    deepLinkingEnabled: Bool = true,
    uninstallMeasurementEnabled: Bool = true,
    conversionDataEnabled: Bool = true,
    adRevenueTrackingEnabled: Bool = false,
    anonymousUserMode: Bool = false,
    minTimeBetweenSessions: TimeInterval = 5,
    customProperties: [String: Any] = [:]
  ) {
    self.devKey = devKey
    self.appleAppID = appleAppID
    self.isDebugEnabled = isDebugEnabled
    self.attWaitTimeout = attWaitTimeout
    self.customUserID = customUserID
    self.deepLinkingEnabled = deepLinkingEnabled
    self.uninstallMeasurementEnabled = uninstallMeasurementEnabled
    self.conversionDataEnabled = conversionDataEnabled
    self.adRevenueTrackingEnabled = adRevenueTrackingEnabled
    self.anonymousUserMode = anonymousUserMode
    self.minTimeBetweenSessions = minTimeBetweenSessions
    self.customProperties = customProperties
  }
}

// MARK: - Predefined Configurations

extension AppsFlyerConfiguration {

  /// Development configuration with debug enabled
  /// - Parameters:
  ///   - devKey: AppsFlyer developer key
  ///   - appleAppID: Apple App ID
  /// - Returns: Debug configuration
  public static func development(devKey: String, appleAppID: String) -> AppsFlyerConfiguration {
    return AppsFlyerConfiguration(
      devKey: devKey,
      appleAppID: appleAppID,
      isDebugEnabled: true,
      attWaitTimeout: 60,
      deepLinkingEnabled: true,
      conversionDataEnabled: true
    )
  }

  /// Production configuration with optimized settings
  /// - Parameters:
  ///   - devKey: AppsFlyer developer key
  ///   - appleAppID: Apple App ID
  /// - Returns: Production configuration
  public static func production(devKey: String, appleAppID: String) -> AppsFlyerConfiguration {
    return AppsFlyerConfiguration(
      devKey: devKey,
      appleAppID: appleAppID,
      isDebugEnabled: false,
      attWaitTimeout: 60,
      deepLinkingEnabled: true,
      uninstallMeasurementEnabled: true,
      conversionDataEnabled: true
    )
  }

  /// Privacy-focused configuration with minimal data collection
  /// - Parameters:
  ///   - devKey: AppsFlyer developer key
  ///   - appleAppID: Apple App ID
  /// - Returns: Privacy-focused configuration
  public static func privacyFocused(devKey: String, appleAppID: String) -> AppsFlyerConfiguration {
    return AppsFlyerConfiguration(
      devKey: devKey,
      appleAppID: appleAppID,
      isDebugEnabled: false,
      attWaitTimeout: 30,
      deepLinkingEnabled: false,
      uninstallMeasurementEnabled: false,
      conversionDataEnabled: false,
      anonymousUserMode: true
    )
  }
}

// MARK: - Configuration Validation

extension AppsFlyerConfiguration {

  /// Validate configuration parameters
  /// - Throws: ConfigurationError if validation fails
  public func validate() throws {
    guard !devKey.isEmpty else {
      throw AppsFlyerConfigurationError.invalidDevKey("Dev key cannot be empty")
    }

    guard !appleAppID.isEmpty else {
      throw AppsFlyerConfigurationError.invalidAppleAppID("Apple App ID cannot be empty")
    }

    guard attWaitTimeout > 0 && attWaitTimeout <= 300 else {
      throw AppsFlyerConfigurationError.invalidTimeout("ATT timeout must be between 1-300 seconds")
    }

    guard minTimeBetweenSessions >= 0 else {
      throw AppsFlyerConfigurationError.invalidSessionInterval(
        "Session interval cannot be negative")
    }
  }
}

// MARK: - Configuration Errors

/// AppsFlyer configuration errors
public enum AppsFlyerConfigurationError: LocalizedError {
  case invalidDevKey(String)
  case invalidAppleAppID(String)
  case invalidTimeout(String)
  case invalidSessionInterval(String)
  case invalidCustomProperty(String)

  public var errorDescription: String? {
    switch self {
    case .invalidDevKey(let message):
      return "Invalid AppsFlyer dev key: \(message)"
    case .invalidAppleAppID(let message):
      return "Invalid Apple App ID: \(message)"
    case .invalidTimeout(let message):
      return "Invalid timeout configuration: \(message)"
    case .invalidSessionInterval(let message):
      return "Invalid session interval: \(message)"
    case .invalidCustomProperty(let message):
      return "Invalid custom property: \(message)"
    }
  }
}
