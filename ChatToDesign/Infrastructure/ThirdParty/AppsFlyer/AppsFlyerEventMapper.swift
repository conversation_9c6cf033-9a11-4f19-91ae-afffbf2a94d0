//
//  AppsFlyerEventMapper.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/1/21.
//

import AppsFlyerLib
import Foundation

/// Maps generic AnalyticsEvent to AppsFlyer-specific events and parameters
public final class AppsFlyerEventMapper {

  // MARK: - Event Name Mapping

  /// Map generic event name to AppsFlyer event name
  /// - Parameter eventName: Generic event name
  /// - Returns: AppsFlyer event name
  public static func mapEventName(_ eventName: String) -> String {
    switch eventName.lowercased() {
    // User lifecycle events
    case "user_signup", "sign_up", "registration":
      return AFEventCompleteRegistration
    case "user_signin", "sign_in", "login":
      return AFEventLogin
    case "user_signout", "sign_out", "logout":
      return "af_logout"

    // Purchase events
    case "purchase", "subscription_purchase", "in_app_purchase":
      return AFEventPurchase
    case "subscription_started", "subscription_begin":
      return AFEventStartTrial
    case "subscription_cancelled", "subscription_cancel":
      return "af_subscription_cancelled"

    // Content events
    case "content_view", "screen_view", "page_view":
      return AFEventContentView
    case "search", "search_query":
      return AFEventSearch
    case "share", "content_share":
      return AFEventShare

    // Engagement events
    case "tutorial_complete", "onboarding_complete":
      return AFEventTutorial_completion

    // E-commerce events
    case "add_to_cart", "cart_add":
      return AFEventAddToCart
    case "add_to_wishlist", "wishlist_add":
      return AFEventAddToWishlist
    case "initiate_checkout", "checkout_begin":
      return AFEventInitiatedCheckout

    // App events
    case "ad_click", "advertisement_click":
      return AFEventAdClick
    case "ad_view", "advertisement_view":
      return AFEventAdView

    // Custom events - keep original name with prefix
    default:
      return sanitizeEventName(eventName)
    }
  }

  // MARK: - Parameter Mapping

  /// Map generic parameters to AppsFlyer parameters
  /// - Parameter parameters: Generic parameters
  /// - Returns: AppsFlyer-compatible parameters
  public static func mapParameters(_ parameters: [String: Any]) -> [String: Any] {
    var mappedParameters: [String: Any] = [:]

    for (key, value) in parameters {
      let mappedKey = mapParameterKey(key)
      let mappedValue = mapParameterValue(value)
      mappedParameters[mappedKey] = mappedValue
    }

    return mappedParameters
  }

  /// Map parameter key to AppsFlyer standard key
  /// - Parameter key: Generic parameter key
  /// - Returns: AppsFlyer parameter key
  public static func mapParameterKey(_ key: String) -> String {
    switch key.lowercased() {
    // Revenue parameters
    case "value", "amount", "price", "revenue":
      return AFEventParamRevenue
    case "currency", "currency_code":
      return AFEventParamCurrency
    case "quantity", "count":
      return AFEventParamQuantity

    // Content parameters
    case "content_type", "item_category", "category":
      return AFEventParamContentType
    case "content_id", "item_id", "product_id":
      return AFEventParamContentId
    case "content_list", "item_list":
      return AFEventParamContentList

    // User parameters
    case "user_id", "customer_id":
      return "af_customer_user_id"
    case "level", "user_level":
      return AFEventParamLevel
    case "score", "user_score":
      return AFEventParamScore

    // Search parameters
    case "search_string", "query", "search_term":
      return AFEventParamSearchString
    case "success", "search_success":
      return AFEventParamSuccess

    // Registration parameters
    case "registration_method", "signup_method", "method":
      return AFEventParamRegistrationMethod

    // Custom parameters - sanitize key
    default:
      return sanitizeParameterKey(key)
    }
  }

  /// Map parameter value to AppsFlyer-compatible type
  /// - Parameter value: Generic parameter value
  /// - Returns: AppsFlyer-compatible value
  private static func mapParameterValue(_ value: Any) -> Any {
    switch value {
    case let stringValue as String:
      return stringValue
    case let intValue as Int:
      return intValue
    case let doubleValue as Double:
      return doubleValue
    case let floatValue as Float:
      return Double(floatValue)
    case let boolValue as Bool:
      return boolValue ? "true" : "false"
    case let arrayValue as [Any]:
      return arrayValue.compactMap { mapParameterValue($0) }
    case let dictValue as [String: Any]:
      return mapParameters(dictValue)
    default:
      return String(describing: value)
    }
  }

  // MARK: - Revenue Event Mapping

  /// Create AppsFlyer revenue event parameters
  /// - Parameters:
  ///   - revenue: Revenue amount
  ///   - currency: Currency code
  ///   - productId: Product identifier
  ///   - quantity: Quantity purchased
  /// - Returns: AppsFlyer revenue parameters
  public static func createRevenueParameters(
    revenue: Double,
    currency: String,
    productId: String? = nil,
    quantity: Int = 1
  ) -> [String: Any] {
    var parameters: [String: Any] = [
      AFEventParamRevenue: revenue,
      AFEventParamCurrency: currency,
      AFEventParamQuantity: quantity,
    ]

    if let productId = productId {
      parameters[AFEventParamContentId] = productId
    }

    return parameters
  }

  // MARK: - Sanitization

  /// Sanitize event name for AppsFlyer
  /// - Parameter eventName: Raw event name
  /// - Returns: Sanitized event name
  private static func sanitizeEventName(_ eventName: String) -> String {
    let sanitized =
      eventName
      .lowercased()
      .replacingOccurrences(of: " ", with: "_")
      .replacingOccurrences(of: "-", with: "_")
      .filter { $0.isLetter || $0.isNumber || $0 == "_" }

    // Ensure it starts with a letter
    if sanitized.first?.isLetter != true {
      return "af_\(sanitized)"
    }

    return sanitized
  }

  /// Sanitize parameter key for AppsFlyer
  /// - Parameter key: Raw parameter key
  /// - Returns: Sanitized parameter key
  private static func sanitizeParameterKey(_ key: String) -> String {
    let sanitized =
      key
      .lowercased()
      .replacingOccurrences(of: " ", with: "_")
      .replacingOccurrences(of: "-", with: "_")
      .filter { $0.isLetter || $0.isNumber || $0 == "_" }

    // Limit length to 40 characters (AppsFlyer limit)
    let maxLength = 40
    if sanitized.count > maxLength {
      return String(sanitized.prefix(maxLength))
    }

    return sanitized
  }
}

// MARK: - Event Category Mapping

extension AppsFlyerEventMapper {

  /// Determine if event should be sent to AppsFlyer based on category
  /// - Parameter event: Analytics event
  /// - Returns: True if event should be sent to AppsFlyer
  public static func shouldTrackEvent(_ event: AnalyticsEvent) -> Bool {
    // Marketing attribution events
    let marketingEvents = [
      "deep_link_opened",
      "campaign_click",
      "ad_click",
      "ad_view",
      "install_attributed",
      "first_open",
    ]

    // Conversion events
    let conversionEvents = [
      "purchase",
      "subscription_purchase",
      "subscription_started",
      "user_signup",
      "tutorial_complete",
      "level_achieved",
    ]

    // Revenue events
    let revenueEvents = [
      "purchase",
      "subscription_purchase",
      "in_app_purchase",
      "add_to_cart",
      "initiate_checkout",
    ]

    let eventName = event.name.lowercased()

    return marketingEvents.contains(eventName) || conversionEvents.contains(eventName)
      || revenueEvents.contains(eventName) || event.category == .marketing
      || event.category == .conversion || event.category == .revenue
  }
}
