# AppsFlyer OneLink Deep Linking Integration Plan

## 项目概述

本文档详细说明如何在 ChatToDesign iOS 应用中集成 AppsFlyer OneLink 深度链接功能，使用 SwiftUI 原生的 URL 处理方式和 iOS Universal Links 作为主要实现方式。

> **📱 SwiftUI 架构**: 本文档已更新为使用 SwiftUI 原生的 URL 处理方式，不再使用 SceneDelegate。

## 当前架构分析

### 现有 AppsFlyer 集成状态

- ✅ AppsFlyer SDK 已集成 (`AppsFlyerLib`)
- ✅ 基础配置已完成 (`AppsFlyerAnalyticsAdapter`)
- ✅ 深度链接处理器已实现 (`AppsFlyerDeepLinkHandler`)
- ✅ 依赖注入容器已配置 (`AppDependencyContainer`)
- ✅ Universal Links 域名已配置 (`picadabra-ai.onelink.me`)

### 现有 URL 处理

- ✅ Google Sign-In URL Scheme 已配置
- ✅ SwiftUI 原生 URL 处理已实现 (`.onOpenURL()`)
- ✅ Universal Links 处理已实现 (`.onContinueUserActivity()`)
- ✅ AppDelegate 中保留基础 URL 处理作为备用

## 技术方案

### 1. Universal Links 完整配置

#### 1.1 Apple-App-Site-Association (AASA) 文件

AppsFlyer 会自动为你的 OneLink 域名生成 AASA 文件，位于：

```
https://picadabra-ai.onelink.me/.well-known/apple-app-site-association
```

AASA 文件内容示例：

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "TEAM_ID.com.a1d.chat-to-design",
        "paths": ["*"]
      }
    ]
  }
}
```

#### 1.2 Entitlements 配置

当前已配置：

```xml
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:picadabra-ai.onelink.me</string>
</array>
```

#### 1.3 SwiftUI 原生 URL 处理实现

✅ **已实现**: 使用 SwiftUI 原生的 URL 处理方式，这是 SwiftUI 应用的最佳实践。

##### 1.3.1 ChatToDesignApp.swift 实现

已更新为使用 SwiftUI 原生 URL 处理：

```swift
@main
struct ChatToDesignApp: App {
  // Register AppDelegate
  @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

  // Environment variables
  @Environment(\.scenePhase) private var scenePhase

  var body: some Scene {
    WindowGroup {
      ZStack {
        RootView()
      }
      .preferredColorScheme(.dark)
      .onOpenURL { url in
        handleURL(url)
      }
      .onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
        handleUniversalLink(userActivity)
      }
    }
    .onChange(of: scenePhase) { newPhase in
      // Handle scene phase changes
    }
  }

  // MARK: - URL Handling

  private func handleURL(_ url: URL) {
    Logger.info("App: Processing URL Scheme - \(url.absoluteString)")

    // Handle Google Sign-In URL
    if GIDSignIn.sharedInstance.handle(url) {
      Logger.info("App: Google Sign-In URL handled")
      return
    }

    // Handle AppsFlyer deep links
    let container = AppDependencyContainer.shared
    container.deepLinkHandler.processDeepLink(from: url)

    Logger.info("App: URL Scheme processed successfully")
  }

  private func handleUniversalLink(_ userActivity: NSUserActivity) {
    guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
          let url = userActivity.webpageURL else {
      Logger.warning("App: Invalid user activity for Universal Link")
      return
    }

    Logger.info("App: Processing Universal Link - \(url.absoluteString)")

    // Notify AppsFlyer SDK
    AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

    // Handle deep link routing
    let container = AppDependencyContainer.shared
    container.deepLinkHandler.processDeepLink(from: url)

    Logger.info("App: Universal Link processed successfully")
  }
}
```

##### 1.3.2 Info.plist 配置

SwiftUI 应用不需要 Scene 配置，只需要 URL Schemes：

```xml
<key>CFBundleURLTypes</key>
<array>
  <!-- Google Sign-In URL Scheme -->
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>com.googleusercontent.apps.************-a5d47f24ekno52pqddtqea2qevtoit8f</string>
    </array>
  </dict>

  <!-- Custom URL Scheme -->
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLName</key>
    <string>com.a1d.chat-to-design</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>chattodesign</string>
    </array>
  </dict>
</array>
```

### 2. AppsFlyer UDL (Unified Deep Linking) 集成

#### 2.1 配置 Deep Link Delegate

在 `AppsFlyerAnalyticsAdapter` 中已实现，需要确保：

```swift
// 在配置时设置 delegate
AppsFlyerLib.shared().deepLinkDelegate = self
```

#### 2.2 实现 AppsFlyerDeepLinkDelegate

```swift
extension AppsFlyerAnalyticsAdapter: AppsFlyerDeepLinkDelegate {
    func didResolveDeepLink(_ result: AppsFlyerDeepLinkResult) {
        // 委托给现有的 DeepLinkHandler 处理
        let container = AppDependencyContainer.shared
        container.deepLinkHandler.processDeepLink(result)
    }
}
```

### 3. OneLink 链接结构设计

#### 3.1 基础链接格式

```
https://picadabra-ai.onelink.me/{template_id}?pid={media_source}&deep_link_value={destination}&deep_link_sub1={parameter1}
```

#### 3.2 深度链接值映射

| deep_link_value | 目标页面        | 描述         |
| --------------- | --------------- | ------------ |
| `create`        | CreatePageView  | 创建页面     |
| `explore`       | ExplorePageView | 探索页面     |
| `profile`       | ProfilePageView | 个人资料页面 |
| `subscription`  | PaywallView     | 订阅页面     |
| `chat`          | 聊天功能        | 特定聊天会话 |
| `template`      | 模板详情        | 特定模板页面 |

#### 3.3 参数设计

- `deep_link_sub1`: 具体 ID（如 template_id, chat_id）
- `deep_link_sub2`: 额外参数（如 campaign_id）
- `deep_link_sub3`: 用户标识或推荐码
- `campaign`: 营销活动名称
- `media_source`: 流量来源

### 4. 路由系统增强

#### 4.1 现有路由通知扩展

```swift
extension Notification.Name {
    // 现有通知
    static let deepLinkRouteToChat = Notification.Name("deepLinkRouteToChat")
    static let deepLinkRouteToSubscription = Notification.Name("deepLinkRouteToSubscription")

    // 新增通知
    static let deepLinkRouteToTemplate = Notification.Name("deepLinkRouteToTemplate")
    static let deepLinkRouteToSpecificChat = Notification.Name("deepLinkRouteToSpecificChat")
}
```

#### 4.2 MainTabView 路由处理

在 `MainTabView` 中监听深度链接通知：

```swift
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToCreate)) { _ in
    selectedTab = .create
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToExplore)) { _ in
    selectedTab = .explore
}
```

### 5. 实现步骤

#### 阶段 1: 基础 Universal Links 支持

1. ✅ SwiftUI 原生 URL 处理已实现
2. ✅ AppsFlyer Deep Link Delegate 正确配置
3. ✅ 基础 Universal Links 功能已测试

#### 阶段 2: UDL 集成

1. 实现 `AppsFlyerDeepLinkDelegate` 协议
2. 增强 `AppsFlyerDeepLinkHandler` 路由逻辑
3. 添加新的路由通知和处理

#### 阶段 3: UI 路由集成

1. 在 MainTabView 中添加深度链接监听
2. 实现具体页面的参数处理
3. 添加错误处理和回退逻辑

#### 阶段 4: 测试和优化

1. 创建测试 OneLink 链接
2. 测试各种场景（新用户、现有用户、应用未安装）
3. 性能优化和错误处理完善

### 6. 测试策略

#### 6.1 测试场景

- **新用户安装**: 点击链接 → App Store → 安装 → 首次启动 → 延迟深度链接
- **现有用户**: 点击链接 → 直接打开应用 → 即时深度链接
- **应用在后台**: 点击链接 → 应用前台 → 深度链接处理

#### 6.2 测试链接示例

```
# 跳转到创建页面
https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=create

# 跳转到特定模板
https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=template&deep_link_sub1=video_template_001

# 跳转到订阅页面（带营销参数）
https://picadabra-ai.onelink.me/H5hv?pid=email&c=summer_sale&deep_link_value=subscription&deep_link_sub1=premium
```

### 7. 监控和分析

#### 7.1 关键指标

- 深度链接点击率
- 安装转化率
- 深度链接成功率
- 用户路径分析

#### 7.2 错误监控

- 深度链接解析失败
- 路由目标不存在
- 网络连接问题
- AASA 文件访问失败

## 技术风险和缓解策略

### 风险 1: Universal Links 不工作

**缓解**: 实现 URL Scheme 作为备用方案

### 风险 2: AASA 文件缓存问题

**缓解**: 提供手动刷新机制和错误提示

### 风险 3: 深度链接参数丢失

**缓解**: 实现本地缓存和重试机制

### 风险 4: 用户体验中断

**缓解**: 优雅的错误处理和默认页面跳转

## 下一步行动

1. **立即执行**: 更新 AppDelegate 支持 Universal Links
2. **本周完成**: 实现完整的 UDL 集成
3. **下周测试**: 创建测试链接并验证功能
4. **持续优化**: 根据用户反馈和数据分析持续改进

## 详细实现代码

### SwiftUI URL 处理实现

SwiftUI 应用使用原生的 URL 处理方式，不需要在 AppDelegate 中添加额外的方法。所有 URL 处理都在 `ChatToDesignApp.swift` 中通过 `.onOpenURL()` 和 `.onContinueUserActivity()` 修饰符完成。

```swift
// 在 ChatToDesignApp.swift 中已实现
.onOpenURL { url in
  handleURL(url)
}
.onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
  handleUniversalLink(userActivity)
}
```

### AppsFlyerAnalyticsAdapter 增强

```swift
// 在 configureAppsFlyerSDK 方法中确保设置 delegate
private func configureAppsFlyerSDK(with config: AppsFlyerConfiguration) async {
    // ... 现有配置代码 ...

    // 确保设置深度链接代理
    if config.deepLinkingEnabled {
        AppsFlyerLib.shared().deepLinkDelegate = self
    }
}

// 实现 AppsFlyerDeepLinkDelegate
extension AppsFlyerAnalyticsAdapter: AppsFlyerDeepLinkDelegate {
    public func didResolveDeepLink(_ result: AppsFlyerDeepLinkResult) {
        logger.info("AppsFlyer UDL: Deep link resolved with status: \(result.status)")

        // 委托给专门的深度链接处理器
        let container = AppDependencyContainer.shared
        container.deepLinkHandler.processDeepLink(result)
    }
}
```

### AppsFlyerDeepLinkHandler 增强

```swift
// 添加 UDL 结果处理方法
public func processDeepLink(_ result: AppsFlyerDeepLinkResult) {
    isProcessingDeepLink = true

    switch result.status {
    case .found:
        handleUDLDeepLinkFound(result)
    case .notFound:
        handleDeepLinkNotFound()
    case .failure:
        if let error = result.error {
            handleDeepLinkFailure(error)
        }
    @unknown default:
        handleUnknownDeepLinkStatus()
    }

    isProcessingDeepLink = false
}

private func handleUDLDeepLinkFound(_ result: AppsFlyerDeepLinkResult) {
    guard let deepLink = result.deepLink else {
        logger.warning("UDL: Deep link found but no data available")
        return
    }

    logger.info("UDL: Processing deep link - isDeferred: \(deepLink.isDeferred)")

    // 提取参数
    let clickEvent = deepLink.clickEvent
    deepLinkData = clickEvent

    // 创建参数对象
    let parameters = extractDeepLinkParameters(from: clickEvent)

    // 如果有 deeplinkValue，优先使用
    if let deeplinkValue = deepLink.deeplinkValue {
        parameters.deepLinkValue = deeplinkValue
    }

    // 路由到目标
    routeToDestination(parameters: parameters)

    // 追踪事件
    trackDeepLinkEvent(parameters: parameters.allParameters, status: "udl_found")
}
```

### MainTabView 路由监听

```swift
// 在 MainTabView 的 body 中添加更多深度链接监听
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToCreate)) { _ in
    selectedTab = .create
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToExplore)) { _ in
    selectedTab = .explore
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToSubscription)) { _ in
    // 显示订阅页面的逻辑
    NotificationCenter.default.post(name: .showSubscriptionPage, object: nil)
}
```

## 配置检查清单

### ✅ 已完成项目

- [x] AppsFlyer SDK 集成
- [x] 基础配置和依赖注入
- [x] Universal Links 域名配置 (`picadabra-ai.onelink.me`)
- [x] 深度链接处理器基础实现
- [x] 路由通知系统
- [x] SwiftUI 原生 URL 处理实现
- [x] ChatToDesignApp.swift 更新为使用 `.onOpenURL()` 和 `.onContinueUserActivity()`
- [x] URL Scheme 配置

### 🔄 需要完成项目

- [ ] AppsFlyerAnalyticsAdapter 实现 `AppsFlyerDeepLinkDelegate`
- [ ] AppsFlyerDeepLinkHandler 添加 UDL 支持
- [ ] MainTabView 添加深度链接路由监听
- [ ] 创建和测试 OneLink 模板

### 🧪 测试计划

- [ ] 验证 AASA 文件可访问性

  ```bash
  # 使用 curl 验证 AASA 文件
  curl -v https://picadabra-ai.onelink.me/.well-known/apple-app-site-association
  ```

- [ ] 测试 Universal Links 基础功能

  ```
  # 在 Safari 中打开测试链接
  https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=create
  ```

- [ ] 测试延迟深度链接（新用户安装）

  1. 卸载应用
  2. 点击深度链接
  3. 安装应用
  4. 验证首次启动时是否路由到正确页面

- [ ] 测试即时深度链接（现有用户）

  1. 确保应用已安装
  2. 点击深度链接
  3. 验证是否直接路由到正确页面

- [ ] 测试各种参数组合

  ```
  # 基础路由测试
  https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=create

  # 带参数的路由测试
  https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=template&deep_link_sub1=template_id_123

  # 营销参数测试
  https://picadabra-ai.onelink.me/H5hv?pid=email&c=promo&deep_link_value=subscription
  ```

## 架构变更说明

### 从 SceneDelegate 迁移到 SwiftUI 原生处理

本项目已从传统的 SceneDelegate 方式迁移到 SwiftUI 原生的 URL 处理方式：

**变更前 (SceneDelegate 方式)**:

- 使用 `@UIApplicationDelegateAdaptor(SceneDelegate.self)`
- 在 SceneDelegate 中处理 URL
- 需要 Info.plist 中的 Scene 配置

**变更后 (SwiftUI 原生方式)**:

- 使用 `.onOpenURL()` 和 `.onContinueUserActivity()` 修饰符
- 直接在 SwiftUI App 中处理 URL
- 不需要 Scene 配置，简化了架构

**优势**:

- ✅ 符合 SwiftUI 最佳实践
- ✅ 简化了应用架构
- ✅ 减少了 UIKit 和 SwiftUI 的混合使用
- ✅ 更好的代码可维护性

## 相关文档

- [SwiftUI URL Handling Best Practices](./swiftui-url-handling-best-practices.md) - **推荐阅读**
- [AppsFlyer iOS UDL 官方文档](https://dev.appsflyer.com/hc/docs/dl_ios_unified_deep_linking)
- [Apple Universal Links 文档](https://developer.apple.com/documentation/xcode/supporting-universal-links-in-your-app)
- [项目现有 AppsFlyer 集成文档](./AppsFlyer-Integration-Plan.md)
