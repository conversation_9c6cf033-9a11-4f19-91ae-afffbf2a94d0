# SwiftUI URL Handling Best Practices

## 概述

本文档描述了在 SwiftUI 应用中处理 URL schemes 和 Universal Links 的最佳实践。我们已经从传统的 SceneDelegate 方式迁移到 SwiftUI 原生的 URL 处理方式。

## 架构决策

### 为什么不使用 SceneDelegate

1. **架构冲突**：`@UIApplicationDelegateAdaptor` 只能用于符合 `UIApplicationDelegate` 协议的类，而 `SceneDelegate` 符合 `UIWindowSceneDelegate` 协议
2. **SwiftUI 原生支持**：SwiftUI 提供了 `.onOpenURL()` 和 `.onContinueUserActivity()` 修饰符来处理 URL
3. **简化架构**：避免混合 UIKit 和 SwiftUI 的生命周期管理

### 当前实现方式

我们使用 SwiftUI 的原生 URL 处理方式：

```swift
@main
struct ChatToDesignApp: App {
  @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
  @Environment(\.scenePhase) private var scenePhase

  var body: some Scene {
    WindowGroup {
      ZStack {
        RootView()
      }
      .preferredColorScheme(.dark)
      .onOpenURL { url in
        handleURL(url)
      }
      .onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
        handleUniversalLink(userActivity)
      }
    }
  }
}
```

## URL 处理实现

### URL Schemes 处理

```swift
private func handleURL(_ url: URL) {
  Logger.info("App: Processing URL Scheme - \(url.absoluteString)")

  // Handle Google Sign-In URL
  if GIDSignIn.sharedInstance.handle(url) {
    Logger.info("App: Google Sign-In URL handled")
    return
  }

  // Handle AppsFlyer deep links
  let container = AppDependencyContainer.shared
  container.deepLinkHandler.processDeepLink(from: url)

  Logger.info("App: URL Scheme processed successfully")
}
```

### Universal Links 处理

```swift
private func handleUniversalLink(_ userActivity: NSUserActivity) {
  guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
        let url = userActivity.webpageURL else {
    Logger.warning("App: Invalid user activity for Universal Link")
    return
  }

  Logger.info("App: Processing Universal Link - \(url.absoluteString)")

  // Notify AppsFlyer SDK
  AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

  // Handle deep link routing
  let container = AppDependencyContainer.shared
  container.deepLinkHandler.processDeepLink(from: url)

  Logger.info("App: Universal Link processed successfully")
}
```

## Info.plist 配置

### URL Schemes 配置

```xml
<key>CFBundleURLTypes</key>
<array>
  <!-- Google Sign-In URL Scheme -->
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>com.googleusercontent.apps.************-a5d47f24ekno52pqddtqea2qevtoit8f</string>
    </array>
  </dict>
  
  <!-- Custom URL Scheme -->
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLName</key>
    <string>com.a1d.chat-to-design</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>chattodesign</string>
    </array>
  </dict>
</array>
```

### 重要说明

- **不需要** `UIApplicationSceneManifest` 配置
- **不需要** `UISceneDelegateClassName` 配置
- SwiftUI 自动管理场景生命周期

## 错误处理

```swift
private func handleDeepLinkError(_ error: Error) {
  Logger.error("App: Deep link processing failed - \(error.localizedDescription)")
  
  // Handle error by routing to default destination
  DispatchQueue.main.async {
    NotificationCenter.default.post(
      name: .deepLinkRouteToDefault,
      object: nil,
      userInfo: ["error": error.localizedDescription]
    )
  }
}
```

## 测试

### URL Schemes 测试

```bash
# 测试自定义 URL scheme
xcrun simctl openurl booted "chattodesign://profile"

# 测试 Google Sign-In URL
xcrun simctl openurl booted "com.googleusercontent.apps.************-a5d47f24ekno52pqddtqea2qevtoit8f://oauth"
```

### Universal Links 测试

```bash
# 测试 Universal Link
xcrun simctl openurl booted "https://your-domain.com/deeplink"
```

## 迁移指南

如果你的项目之前使用 SceneDelegate，请按以下步骤迁移：

1. **移除 SceneDelegate 注册**：从 SwiftUI App 中移除 `@UIApplicationDelegateAdaptor(SceneDelegate.self)`
2. **添加 SwiftUI URL 处理**：使用 `.onOpenURL()` 和 `.onContinueUserActivity()`
3. **更新 Info.plist**：移除 `UIApplicationSceneManifest` 配置
4. **删除 SceneDelegate 文件**：如果不再需要，可以删除 SceneDelegate.swift 文件

## 参考资料

- [Apple Developer Documentation - onOpenURL](https://developer.apple.com/documentation/swiftui/view/onopenurl(perform:))
- [Apple Developer Documentation - onContinueUserActivity](https://developer.apple.com/documentation/swiftui/view/oncontinueuseractivity(_:perform:))
- [SwiftUI App Lifecycle Best Practices](https://www.jessesquires.com/blog/2024/06/29/swiftui-scene-phase/)
